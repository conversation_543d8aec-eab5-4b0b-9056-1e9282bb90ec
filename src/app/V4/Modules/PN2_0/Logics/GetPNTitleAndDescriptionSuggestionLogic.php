<?php

namespace App\V4\Modules\PN2_0\Logics;

use App\Services\GeminiApi\Services\SuggestsPNTitleAndDescription;
use App\V4\Exceptions\ArticleNotFoundException;
use App\V4\Exceptions\VideoNotFoundException;
use App\V4\Repositories\ArticleRepository;
use App\V4\Repositories\VideoRepository;

class GetPNTitleAndDescriptionSuggestionLogic
{
    private SuggestsPNTitleAndDescription $suggestsPNTitleAndDescription;
    private ArticleRepository $articleRepository;
    private VideoRepository $videoRepository;

    public function __construct(
        SuggestsPNTitleAndDescription $suggestsPNTitleAndDescription,
        ArticleRepository $articleRepository,
        VideoRepository $videoRepository
    ) {
        $this->suggestsPNTitleAndDescription = $suggestsPNTitleAndDescription;
        $this->articleRepository = $articleRepository;
        $this->videoRepository = $videoRepository;
    }

    public function execute(string $uniqueId): array {
        if (getEntityType($uniqueId) === 'v') {
            return $this->handleVideoContent($uniqueId);
        }

        return $this->handleArticleContent($uniqueId);
    }

    private function handleArticleContent(string $uniqueId): array {
        $article = $this->articleRepository->findBy('uniqueId', $uniqueId);

        if (empty($article) === true) {
            throw new ArticleNotFoundException();
        }

        $currentArticleLanguage = $article->channel->language;

        return $this->getSuggestions($article->html, $currentArticleLanguage);
    }

    private function handleVideoContent(string $uniqueId): array {
        $video = $this->videoRepository->findBy('unique_id', $uniqueId);

        if (empty($video) === true) {
            throw new VideoNotFoundException();
        }

        $currentVideoLanguage = $video->channel->language;

        return $this->getSuggestions($video->title . ' ' . $video->content, $currentVideoLanguage);
    }

    private function getSuggestions(string $content, string $currentLanguage): array {
        $suggestionsText = $this->suggestsPNTitleAndDescription->execute(
            $content,
            $currentLanguage
        );

        preg_match('/\{.*\}/s', $suggestionsText, $matches);

        return json_decode($matches[0], true);
    }
}
